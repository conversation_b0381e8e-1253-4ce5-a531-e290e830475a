# AI SaaS Template

Build Any AI SaaS Startups in hours.

![preview](preview.png)

## Quick Start

1. Clone the repository

```bash
git clone https://github.com/YOUR_USERNAME/ai-saas-template.git
```

2. Install dependencies

```bash
pnpm install
```

3. Run the development server

```bash
pnpm dev
```

## Customize

- Set your environment variables

```bash
cp .env.example .env.local
```

- Set your theme in `app/theme.css`

[shadcn-ui-theme-generator](https://zippystarter.com/tools/shadcn-ui-theme-generator)

- Set your landing page content in `i18n/pages/landing`

- Set your i18n messages in `i18n/messages`

## Deploy

- Deploy to Vercel

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2FYOUR_USERNAME%2Fai-saas-template&project-name=my-ai-saas-project&repository-name=my-ai-saas-project&redirect-url=https%3A%2F%2Fyour-domain.com&demo-title=AI%20SaaS%20Template&demo-description=Build%20Any%20AI%20SaaS%20in%20hours%2C%20not%20days&demo-url=https%3A%2F%2Fyour-domain.com)

- Deploy to Cloudflare

1. Customize your environment variables

```bash
cp .env.example .env.production
cp wrangler.toml.example wrangler.toml
```

edit your environment variables in `.env.production`

and put all the environment variables under `[vars]` in `wrangler.toml`

2. Deploy

```bash
npm run cf:deploy
```

## Community

- [Your Website](https://your-domain.com)
- [Documentation](https://docs.your-domain.com)
- [Discord](https://discord.gg/YOUR_DISCORD_INVITE)

## License

- [AI SaaS Template License Agreement](LICENSE)
